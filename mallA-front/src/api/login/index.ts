
import request from '../../utils/request';
// import other from '../../utils/other';
import other from '../../utils/other'
/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 登录
 * @param data
 */
export const getCaptcha = (data) => {
    return request({
        url: '/captcha',
        method: 'get',
        headers: {
            // skipToken: true,
            // 'Content-Type': 'application/json; charset=UTF-8',
            'X-Captcha-Token' :data,
            'responseType':'Blob'
        },
    });
};
/**
 * 登录
 * @param data
 */
export const login = (data: any) => {
    let encPassword = data.password;
       // 密码加密
  
    return request({
        url: '/mall-project/user/login',
        method: 'post',
        data: {...data}
    });
};
/**
 * 获取用户信息
 */
export const getUserInfo = () => {
    return request({
        url: '/mall-project/modules/getUserModulesTree',
        method: 'post'
    });
};