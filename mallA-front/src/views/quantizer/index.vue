    <template>
    <div class="page-container">
      <div class="content-wrapper">

        <!-- 基础设置卡片 -->
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>Admin量化值统计</span>
            </div>
          </template>
          <div class="setting-row">
            <div class="setting-item">
              <span class="setting-label">每日Admin量化值</span>
            <el-input
                class="setting-input"
              placeholder="请输入量化值"
                v-model="adminDailyQuantifyValue"
                disabled
            />
            </div>
            <div class="setting-item">
              <span class="setting-label">每日累计Admin量化值</span>
            <el-input
                class="setting-input"
              placeholder="请输入量化值"
                v-model="adminDailyQuantity"
                disabled
            />
            </div>
        </div>
        </el-card>

        <!-- 搜索卡片 -->
        <el-card class="search-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据查询</span>
            </div>
          </template>
          
          <div class="search-form">
            <div class="form-row">
              <div class="form-item">
                <span class="form-label">手机号</span>
                <el-input v-model="searchPhone" placeholder="请输入手机号" clearable>
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div class="form-item">
                <span class="form-label">查询日期</span>
                <el-date-picker
                  v-model="searchStartDate"
                  type="date"
                  placeholder="查询日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
            </div>
            </div>

            <div class="form-actions">
              <el-button type="primary" @click="fetchData" :loading="loading">
                <el-icon><Search /></el-icon>搜索
              </el-button>
              <el-button @click="resetSearch">
                <el-icon><Refresh /></el-icon>重置
              </el-button>
              <el-button type="success" @click="exportToExcel">
                <el-icon><Download /></el-icon>导出
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 统计卡片 -->
        <el-card class="stats-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据统计</span>
            </div>
          </template>
          <div class="stats-row">

            <div class="stats-item">
              <div class="stats-title">每日量化值累计</div>
              <el-input
                class="stats-value"
                v-model="everydayTotalQuantizationValue"
                placeholder="0.00"
                disabled
              />
            </div>

            <div class="stats-item">
              <div class="stats-title">总量化值累计</div>
              <el-input
                class="stats-value"
                v-model="allTotalQuantizationValue"
                placeholder="0.00"
                disabled
              />
            </div>

            <div class="stats-item">
              <div class="stats-title">每日量化累计</div>
              <el-input
                class="stats-value"
                v-model="everydayTotalCreditValue"
                placeholder="0.00"
                disabled
              />
            </div>

            <div class="stats-item">
              <div class="stats-title">总量化累计</div>
              <el-input
                class="stats-value"
                v-model="allTotalCreditValue"
                placeholder="0.00"
                disabled
              />
            </div>

            <div class="stats-item">
              <div class="stats-title">每日平台补贴金</div>
              <el-input
                class="stats-value"
                v-model="todayPlatformGold"
                placeholder="0.00"
                disabled
              />
            </div>
            
            <div class="stats-item">
              <div class="stats-title">累计平台补贴金</div>
              <el-input
                class="stats-value"
                v-model="totalPlatformGold"
                placeholder="0.00"
                disabled
              />
            </div>
            
            <div class="stats-item">
              <div class="stats-title">每日平台促销金</div>
              <el-input
                class="stats-value"
                v-model="todayPromotionGold"
                placeholder="0.00"
                disabled
              />
            </div>
            
            <div class="stats-item">
              <div class="stats-title">累计平台促销金</div>
              <el-input
                class="stats-value"
                v-model="totalPromotionGold"
                placeholder="0.00"
                disabled
              />
            </div>
          </div>
        </el-card>

        <!-- 数据表格卡片 -->
        <el-card class="table-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据列表</span>
        </div>
          </template>
          
          <el-table 
            :data="tableData" 
            stripe
            border
            v-loading="loading"
            element-loading-text="加载中..."
            style="width: 100%"
          >
            <el-table-column prop="updateDate" label="日期" width="120" />
            <el-table-column prop="phone" label="手机号" width="120" />
            <el-table-column prop="username" label="名字（名称）" width="120" />
            <el-table-column prop="value" label="量化值" />
            <el-table-column prop="totalValue" label="累计量化值" />
            <el-table-column prop="creditValue" label="量化" />
            <el-table-column prop="totalCreditValue" label="累计量化" />
            <el-table-column prop="platformGold" label="平台补贴金/促销金" width="160" />
            <el-table-column prop="totalPlatformGold" label="累计金" />
          </el-table>

          <!-- 分页器 -->
          <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
              :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
          />
        </div>
        </el-card>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { Search, Refresh, Download, Phone } from '@element-plus/icons-vue';
import request from '../../utils/request';
import { Session } from "../../utils/storage";
import axios from "axios";

// 路由
const router = useRouter();

// 表单数据 - 量化值统计
const adminDailyQuantifyValue = ref(""); // 每日Admin量化值
const adminDailyQuantity = ref(""); // 每日累计Admin量化值
const todayTotalQuantity = ref(""); // 每日总量化值进化量
const totalQuantity = ref(""); // 累计量化值进化量
const todayTotalCredit = ref(""); // 每日总量化
const totalCredit = ref(""); // 累计量化

// 平台金统计
const todayPlatformGold = ref(""); // 每日平台补贴金
const totalPlatformGold = ref(""); // 累计平台补贴金
const todayPromotionGold = ref(""); // 每日平台促销金
const totalPromotionGold = ref(""); // 累计平台促销金

// 额外的统计字段
const everydayTotalQuantizationValue = ref(""); // 每日量化值累计
const allTotalQuantizationValue = ref(""); // 总量化值累计
const everydayTotalCreditValue = ref(""); // 每日量化累计
const allTotalCreditValue = ref(""); // 总量化累计

// 搜索条件
const searchPhone = ref("");
const searchStartDate = ref("");
const searchEndDate = ref("");
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

// 表格数据
const tableData = ref([]);

// 初始化加载数据
onMounted(() => {
  // 获取表格数据和统计数据
  fetchData();
});





// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      phone: searchPhone.value,
      startDate: searchStartDate.value || "",
      endDate: searchEndDate.value || "",
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    const response = await request({
      url: '/mall-project/api/queryQuantizationValuePages',
      method: 'post',
      data: params
    });

    // response.data直接就是业务数据
    const data = response.data;
    if (data && data.list) {
      tableData.value = data.list;
      total.value = data.total;
      currentPage.value = data.pageNum;

      // 同时更新统计数据，因为新接口同时返回统计信息
      if (data.summary) {
        const summary = data.summary;
        // 更新各个统计字段
        adminDailyQuantifyValue.value = summary.adminDailyQuantifyValue?.toString() || "0";           // 每日Admin量化值
        adminDailyQuantity.value = summary.adminDailyQuantizationValue?.toString() || "0";            // 每日累计Admin量化值
        todayTotalQuantity.value = summary.todayTotalQuantizationValue?.toString() || "0";            // 每日总量化值进化量
        totalQuantity.value = summary.totalQuantizationValue?.toString() || "0";                      // 累计量化值进化量
        todayTotalCredit.value = summary.todayTotalCreditValue?.toString() || "0";                    // 每日总量化
        totalCredit.value = summary.totalCreditValue?.toString() || "0";                              // 累计量化

        // 更新平台金统计 - 需要根据新的字段映射
        todayPlatformGold.value = summary.todayTotalPlatformGold?.toString() || "0";                  // 每日平台补贴金
        totalPlatformGold.value = summary.totalPlatformGold?.toString() || "0";                       // 累计平台补贴金
        todayPromotionGold.value = summary.todayTotalPromotionGold?.toString() || "0";                // 每日平台促销金
        totalPromotionGold.value = summary.totalPromotionGold?.toString() || "0";                     // 累计平台促销金

        // 更新额外的统计字段
        everydayTotalQuantizationValue.value = summary.everydayTotalQuantizationValue?.toString() || "0"; // 每日量化值累计
        allTotalQuantizationValue.value = summary.allTotalQuantizationValue?.toString() || "0";           // 总量化值累计
        everydayTotalCreditValue.value = summary.everydayTotalCreditValue?.toString() || "0";             // 每日量化累计
        allTotalCreditValue.value = summary.allTotalCreditValue?.toString() || "0";                       // 总量化累计
      }
    } else {
      ElMessage.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    // 使用后台返回的错误信息
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('获取数据失败，请稍后重试');
    }
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchPhone.value = "";
  searchStartDate.value = "";
  searchEndDate.value = "";
  currentPage.value = 1;
  fetchData();
};

// 导出Excel
const exportToExcel = async () => {
  try {
    const params = {
      phone: searchPhone.value,
      startDate: searchStartDate.value || "",
      endDate: searchEndDate.value || ""
    };

    const token = Session.getToken();
    const response = await axios.post('/mall-project/api/exportQuantizationValueExcel', params, {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    // 创建blob对象并下载
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `量化数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    ElMessage.success('导出成功');
  } catch (error: any) {
    console.error('导出失败:', error);
    ElMessage.error(error?.response?.data?.message || error?.message || "导出失败");
  }
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchData();
};
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;
  
  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

/* 卡片通用样式 */
:deep(.el-card) {
    border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

/* 设置卡片样式 */
.setting-card {
  .setting-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .setting-item {
    flex: 1;
    min-width: 300px;
    display: flex;
    align-items: center;
    margin-right: 24px;
    margin-bottom: 16px;
    
    &:last-child {
      margin-right: 0;
    }
  }
  
  .setting-label {
    width: 160px;
    font-size: 14px;
    color: #262626;
  }
  
  .setting-input {
    width: 240px;
  }
}

/* 搜索卡片样式 */
.search-card {
  .search-form {
    .form-row {
    display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
    }
    
    .form-item {
      display: flex;
      align-items: center;
      margin-right: 32px;
      margin-bottom: 16px;
      
      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }
      
      .el-input, .el-date-picker {
        width: 240px;
      }

      .el-input .el-input__prefix {
        color: #a0a0a0;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;

      .el-button {
        min-width: 100px; // 设置最小宽度防止loading时宽度变化

        &:last-child {
          margin-right: 0;
        }

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

/* 统计卡片样式 */
.stats-card {
  .stats-row {
    display: flex;
    flex-wrap: wrap;
  }
  
  .stats-item {
    flex: 1;
    min-width: 240px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .stats-title {
      font-size: 14px;
      color: #595959;
      margin-bottom: 12px;
    }
    
    .stats-value {
      width: 160px;
    }
  }
}

/* 表格卡片样式 */
.table-card {
  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    
    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }
    
    .cell {
      padding: 12px 16px;
    }
  }
}

.pagination-container {
  margin-top: 24px;
    display: flex;
  justify-content: flex-start; /* Change from flex-end to flex-start to align to the left */
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #d9d9d9 inset;
  
  &:hover {
    box-shadow: 0 0 0 1px #40a9ff inset;
  }
  
  &.is-focus {
    box-shadow: 0 0 0 1px #1890ff inset;
  }
}

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}

:deep(.el-button) {
    border-radius: 4px;
  
  &.el-button--primary {
    --el-button-bg-color: #1890ff;
    --el-button-border-color: #1890ff;
    --el-button-hover-bg-color: #40a9ff;
    --el-button-hover-border-color: #40a9ff;
    --el-button-active-bg-color: #096dd9;
    --el-button-active-border-color: #096dd9;
  }
  
  &.el-button--success {
    --el-button-bg-color: #52c41a;
    --el-button-border-color: #52c41a;
    --el-button-hover-bg-color: #73d13d;
    --el-button-hover-border-color: #73d13d;
    --el-button-active-bg-color: #389e0d;
    --el-button-active-border-color: #389e0d;
  }
}

:deep(.el-pagination) {
  .el-pagination__sizes {
    margin-right: 16px;
  }
  
  .el-pagination__jump {
    margin-left: 16px;
  }
}
</style>